/**
 * ============================================================================
 * AI CONTEXT: Middleware Manager - Priority-based Middleware with Resilient Timing
 * Purpose: Middleware registration, execution chain management, and hook processing
 * Complexity: High - Advanced middleware system with 6 vulnerable patterns enhanced
 * AI Navigation: 4 logical sections - Registration, Execution, Validation, Metrics
 * Dependencies: EventTypes, ResilientTiming, MemorySafeResourceManager, EventUtilities
 * Performance: <2ms middleware processing with resilient measurement
 * ============================================================================
 */

/**
 * @file Middleware Manager
 * @filepath shared/src/base/event-handler-registry/modules/MiddlewareManager.ts
 * @task-id M-TSK-01.SUB-01.1.ENH-02.DAY-12
 * @component event-handler-registry-enhanced
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-event-processing-middleware
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Middleware
 * @created 2025-01-27 16:30:00 +03
 * @modified 2025-01-27 16:30:00 +03
 *
 * @description
 * Priority-based middleware manager for EventHandlerRegistryEnhanced:
 * - Middleware registration with priority-based ordering and validation
 * - Execution chain management with before/after hooks and error handling
 * - Handler execution context management with timing metadata
 * - VULNERABLE PATTERNS ENHANCED: 6 patterns replaced with resilient timing contexts
 * - Performance: <2ms middleware processing with >0.8 reliability score
 * - Anti-Simplification Policy compliance with comprehensive middleware coverage
 */

import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { ILoggingService, SimpleLogger } from '../../LoggingMixin';
import { 
  ResilientTimer,
  IResilientTimingResult,
  ResilientTimingContext 
} from '../../utils/ResilientTiming';
import { 
  ResilientMetricsCollector
} from '../../utils/ResilientMetrics';
import {
  IHandlerMiddleware,
  IHandlerExecutionContext,
  IMiddlewareResult,
  IRegisteredHandler,
  EventHandlerCallback
} from '../types/EventTypes';
import { PERFORMANCE_THRESHOLDS, DEFAULT_MIDDLEWARE_CONFIG } from '../types/EventConfiguration';

// ============================================================================
// SECTION 1: MIDDLEWARE REGISTRATION & MANAGEMENT (Lines 1-120)
// AI Context: "Middleware registration with priority ordering and validation"
// ============================================================================

/**
 * ✅ RESILIENT TIMING: Enhanced middleware manager with timing infrastructure
 */
export class MiddlewareManager extends MemorySafeResourceManager implements ILoggingService {
  // ✅ RESILIENT TIMING: Dual-field pattern per established requirements
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  private _logger: SimpleLogger;

  // Middleware storage and management
  private _middleware: IHandlerMiddleware[] = [];
  private _maxMiddleware: number;

  // Performance tracking for optimization
  private _middlewarePerformanceCache = new Map<string, number>();
  private _reliabilityScores = new Map<string, number>();

  // Metrics tracking
  private _middlewareMetrics = {
    totalMiddlewareExecutions: 0,
    successfulExecutions: 0,
    failedExecutions: 0,
    averageExecutionTime: 0,
    skippedHandlerCount: 0,
    handledErrorCount: 0
  };

  constructor(config?: { maxMiddleware?: number }) {
    super({
      maxIntervals: 10,
      maxTimeouts: 15,
      maxCacheSize: 1 * 1024 * 1024, // 1MB
      memoryThresholdMB: 50,
      cleanupIntervalMs: 300000 // 5 minutes
    });

    this._maxMiddleware = config?.maxMiddleware || DEFAULT_MIDDLEWARE_CONFIG.MAX_MIDDLEWARE_COUNT;
    this._logger = new SimpleLogger('MiddlewareManager');
  }

  // ✅ LOGGING: Implement ILoggingService interface
  public logInfo(message: string, details?: Record<string, unknown>): void {
    this._logger.logInfo(message, details);
  }

  public logWarning(message: string, details?: Record<string, unknown>): void {
    this._logger.logWarning(message, details);
  }

  public logError(message: string, error: unknown, details?: Record<string, unknown>): void {
    this._logger.logError(message, error, details);
  }

  public logDebug(message: string, details?: Record<string, unknown>): void {
    this._logger.logDebug(message, details);
  }

  protected async doInitialize(): Promise<void> {
    // ✅ RESILIENT TIMING: Initialize timing infrastructure
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: PERFORMANCE_THRESHOLDS.MIDDLEWARE_MAX_DURATION_MS * 10, // 20ms max
      unreliableThreshold: 3,
      estimateBaseline: PERFORMANCE_THRESHOLDS.MIDDLEWARE_MAX_DURATION_MS // 2ms baseline
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      cacheUnreliableValues: true,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['middleware_registration', 1],
        ['middleware_execution', PERFORMANCE_THRESHOLDS.MIDDLEWARE_MAX_DURATION_MS]
      ])
    });

    this.logInfo('MiddlewareManager initialized with resilient timing', {
      maxMiddleware: this._maxMiddleware,
      resilientTimingEnabled: true
    });
  }

  protected async doShutdown(): Promise<void> {
    try {
      // Clear all middleware
      this._middleware.length = 0;
      this._middlewarePerformanceCache.clear();
      this._reliabilityScores.clear();

      // Note: ResilientTimer and ResilientMetricsCollector don't have cleanup/shutdown methods
      // They are designed to be lightweight and self-contained

      this.logInfo('MiddlewareManager shutdown completed');
    } catch (error) {
      this.logError('Error during MiddlewareManager shutdown', error);
      throw error;
    }
  }

  /**
   * Add middleware to the execution chain with priority ordering
   * Performance requirement: Registration must be <1ms
   */
  public addMiddleware(middleware: IHandlerMiddleware): void {
    // ✅ RESILIENT TIMING: PATTERN 1 - Replace vulnerable registration timing
    const registrationContext = this._resilientTimer.start();
    
    try {
      // Validate middleware limit
      if (this._middleware.length >= this._maxMiddleware) {
        throw new Error(`Maximum middleware limit reached: ${this._maxMiddleware}`);
      }

      // Validate middleware structure
      this._validateMiddlewareStructure(middleware);

      // Check for duplicate middleware names
      const existingIndex = this._middleware.findIndex(m => m.name === middleware.name);
      if (existingIndex !== -1) {
        throw new Error(`Middleware with name '${middleware.name}' already exists`);
      }

      // Add and sort by priority (higher priority executes first)
      this._middleware.push(middleware);
      this._middleware.sort((a, b) => b.priority - a.priority);

      const timingResult = registrationContext.end();
      
      // ✅ RESILIENT TIMING: Record registration metrics
      this._metricsCollector.recordTiming('middleware_registration', timingResult);

      this.logInfo('Middleware added successfully', {
        name: middleware.name,
        priority: middleware.priority,
        totalMiddleware: this._middleware.length,
        registrationTime: timingResult.duration,
        timingReliability: timingResult.reliable ? 1.0 : 0.5
      });

    } catch (error) {
      registrationContext.end();
      throw this._enhanceErrorWithTimingContext(error, registrationContext);
    }
  }

  /**
   * Remove middleware by name
   */
  public removeMiddleware(name: string): boolean {
    // ✅ RESILIENT TIMING: PATTERN 2 - Replace vulnerable removal timing
    const removalContext = this._resilientTimer.start();
    
    try {
      const index = this._middleware.findIndex(m => m.name === name);
      if (index !== -1) {
        this._middleware.splice(index, 1);
        
        // Clear cached performance data for removed middleware
        this._middlewarePerformanceCache.delete(name);
        this._reliabilityScores.delete(name);

        const timingResult = removalContext.end();
        
        // ✅ RESILIENT TIMING: Record removal metrics
        this._metricsCollector.recordTiming('middleware_removal', timingResult);

        this.logInfo('Middleware removed successfully', {
          name,
          remainingMiddleware: this._middleware.length,
          removalTime: timingResult.duration,
          timingReliability: timingResult.reliable ? 1.0 : 0.5
        });

        return true;
      }

      removalContext.end(); // Still record timing for unsuccessful removal
      return false;

    } catch (error) {
      removalContext.end();
      throw this._enhanceErrorWithTimingContext(error, removalContext);
    }
  }

  /**
   * Get list of registered middleware (sorted by priority)
   */
  public getMiddleware(): readonly IHandlerMiddleware[] {
    return Object.freeze([...this._middleware]);
  }

  /**
   * Get middleware by name
   */
  public getMiddlewareByName(name: string): IHandlerMiddleware | undefined {
    return this._middleware.find(m => m.name === name);
  }

  // ============================================================================
  // SECTION 2: MIDDLEWARE EXECUTION CHAIN (Lines 121-280)
  // AI Context: "Core middleware execution with before/after hooks and error handling"
  // ============================================================================

  /**
   * Execute handler with complete middleware chain
   * Performance requirement: <2ms per middleware execution
   */
  public async executeHandlerWithMiddleware(
    handler: IRegisteredHandler,
    data: unknown,
    eventType: string
  ): Promise<{
    result: unknown;
    success: boolean;
    executionTime: number;
    skippedByMiddleware?: string;
    timingReliability: number;
  }> {
    // ✅ RESILIENT TIMING: PATTERN 3 - Replace vulnerable execution chain timing
    const chainContext = this._resilientTimer.start();
    
    try {
      // Create execution context with timing metadata
      const context: IHandlerExecutionContext = {
        handlerId: handler.id,
        clientId: handler.clientId,
        eventType,
        eventData: data,
        timestamp: new Date(),
        metadata: handler.metadata || {},
        executionAttempt: 1,
        // ✅ RESILIENT TIMING: Enhanced context with timing information
        timingContext: {
          startTime: Date.now(),
          method: 'resilient_timer',
          expectedDuration: PERFORMANCE_THRESHOLDS.MIDDLEWARE_MAX_DURATION_MS
        }
      };

      this._middlewareMetrics.totalMiddlewareExecutions++;

      // Execute beforeHandlerExecution middleware chain
      const skipResult = await this._executeBeforeMiddleware(context);
      if (skipResult.skipped) {
        const timingResult = chainContext.end();
        
        this._middlewareMetrics.skippedHandlerCount++;
        return {
          result: null,
          success: false,
          executionTime: timingResult.duration,
          skippedByMiddleware: skipResult.middlewareName,
          timingReliability: timingResult.reliable ? 1.0 : 0.5
        };
      }

      // Execute the actual handler
      const handlerResult = await this._executeHandler(handler, data, eventType, context);

      // Handle error with middleware if needed
      if (handlerResult.error) {
        const errorHandled = await this._executeErrorMiddleware(context, handlerResult.error);
        if (!errorHandled) {
          throw handlerResult.error;
        }
        this._middlewareMetrics.handledErrorCount++;
      }

      // Execute afterHandlerExecution middleware chain
      await this._executeAfterMiddleware(context, handlerResult.result);

      const chainTimingResult = chainContext.end();
      
      // ✅ RESILIENT TIMING: Record complete chain metrics
      await this._metricsCollector.recordTiming('middleware_chain_complete', chainTimingResult);

      this._middlewareMetrics.successfulExecutions++;
      this._updateMiddlewarePerformanceMetrics(chainTimingResult.duration);

      return {
        result: handlerResult.result,
        success: true,
        executionTime: chainTimingResult.duration,
        timingReliability: chainTimingResult.reliable ? 1.0 : 0.5
      };

    } catch (error) {
      chainContext.end();
      this._middlewareMetrics.failedExecutions++;
      throw this._enhanceErrorWithTimingContext(error, chainContext);
    }
  }

  /**
   * Execute beforeHandlerExecution middleware
   */
  private async _executeBeforeMiddleware(
    context: IHandlerExecutionContext
  ): Promise<{ skipped: boolean; middlewareName?: string }> {
    // ✅ RESILIENT TIMING: PATTERN 4 - Replace vulnerable before middleware timing
    for (const middleware of this._middleware) {
      if (middleware.beforeHandlerExecution) {
        const stepContext = this._resilientTimer.start();
        
        try {
          const shouldContinue = await middleware.beforeHandlerExecution(context);
          const stepResult = stepContext.end();
          
          // ✅ RESILIENT TIMING: Record individual middleware step timing
          await this._metricsCollector.recordTiming('middleware_before_step', stepResult);
          
          if (!shouldContinue) {
            this.logDebug('Handler skipped by middleware', {
              middlewareName: middleware.name,
              handlerId: context.handlerId,
              stepTime: stepResult.duration
            });
            
            return { skipped: true, middlewareName: middleware.name };
          }

        } catch (error) {
          stepContext.end();
          this.logError(`Before middleware '${middleware.name}' failed`, error, {
            handlerId: context.handlerId,
            eventType: context.eventType
          });
          throw this._enhanceErrorWithTimingContext(error, stepContext);
        }
      }
    }

    return { skipped: false };
  }

  /**
   * Execute afterHandlerExecution middleware
   */
  private async _executeAfterMiddleware(
    context: IHandlerExecutionContext,
    result: unknown
  ): Promise<void> {
    // ✅ RESILIENT TIMING: PATTERN 5 - Replace vulnerable after middleware timing
    for (const middleware of this._middleware) {
      if (middleware.afterHandlerExecution) {
        const stepContext = this._resilientTimer.start();
        
        try {
          await middleware.afterHandlerExecution(context, result);
          const stepResult = stepContext.end();
          
          // ✅ RESILIENT TIMING: Record individual middleware step timing
          await this._metricsCollector.recordTiming('middleware_after_step', stepResult);

        } catch (error) {
          stepContext.end();
          this.logError(`After middleware '${middleware.name}' failed`, error, {
            handlerId: context.handlerId,
            eventType: context.eventType
          });
          throw this._enhanceErrorWithTimingContext(error, stepContext);
        }
      }
    }
  }

  /**
   * Execute error handling middleware
   */
  private async _executeErrorMiddleware(
    context: IHandlerExecutionContext,
    error: Error
  ): Promise<boolean> {
    // ✅ RESILIENT TIMING: PATTERN 6 - Replace vulnerable error middleware timing
    for (const middleware of this._middleware) {
      if (middleware.onHandlerError) {
        const stepContext = this._resilientTimer.start();
        
        try {
          const errorHandled = await middleware.onHandlerError(context, error);
          const stepResult = stepContext.end();
          
          // ✅ RESILIENT TIMING: Record error handling timing
          await this._metricsCollector.recordTiming('middleware_error_step', stepResult);
          
          if (errorHandled) {
            this.logDebug('Error handled by middleware', {
              middlewareName: middleware.name,
              handlerId: context.handlerId,
              errorMessage: error.message,
              stepTime: stepResult.duration
            });
            return true;
          }

        } catch (middlewareError) {
          stepContext.end();
          this.logError(`Error middleware '${middleware.name}' failed`, middlewareError, {
            handlerId: context.handlerId,
            originalError: error.message
          });
          // Continue to next middleware instead of throwing
        }
      }
    }

    return false;
  }

  // ============================================================================
  // SECTION 3: HANDLER EXECUTION & VALIDATION (Lines 281-360)
  // AI Context: "Handler execution with comprehensive error handling and validation"
  // ============================================================================

  /**
   * Execute individual handler with error handling
   */
  private async _executeHandler(
    handler: IRegisteredHandler,
    data: unknown,
    eventType: string,
    context: IHandlerExecutionContext
  ): Promise<{ result: unknown; error?: Error; executionTime: number }> {
    const handlerContext = this._resilientTimer.start();
    
    try {
      const result = await handler.callback(data, {
        eventType,
        clientId: handler.clientId,
        timestamp: context.timestamp,
        metadata: context.metadata
      });

      const timingResult = handlerContext.end();
      
      // ✅ RESILIENT TIMING: Record handler execution timing
      await this._metricsCollector.recordTiming('handler_execution', timingResult);

      return {
        result,
        executionTime: timingResult.duration
      };

    } catch (error) {
      const timingResult = handlerContext.end();
      
      return {
        result: null,
        error: error instanceof Error ? error : new Error(String(error)),
        executionTime: timingResult.duration || 0
      };
    }
  }

  /**
   * Validate middleware structure and requirements
   */
  private _validateMiddlewareStructure(middleware: IHandlerMiddleware): void {
    if (!middleware.name || typeof middleware.name !== 'string') {
      throw new Error('Middleware must have a valid name');
    }

    if (typeof middleware.priority !== 'number' || isNaN(middleware.priority)) {
      throw new Error('Middleware must have a valid numeric priority');
    }

    if (middleware.priority < DEFAULT_MIDDLEWARE_CONFIG.PRIORITY_MIN || 
        middleware.priority > DEFAULT_MIDDLEWARE_CONFIG.PRIORITY_MAX) {
      throw new Error(`Middleware priority must be between ${DEFAULT_MIDDLEWARE_CONFIG.PRIORITY_MIN} and ${DEFAULT_MIDDLEWARE_CONFIG.PRIORITY_MAX}`);
    }

    // At least one hook method must be defined
    if (!middleware.beforeHandlerExecution && 
        !middleware.afterHandlerExecution && 
        !middleware.onHandlerError) {
      throw new Error('Middleware must implement at least one hook method');
    }

    this.logDebug('Middleware validation successful', {
      name: middleware.name,
      priority: middleware.priority,
      hooks: {
        beforeHandler: !!middleware.beforeHandlerExecution,
        afterHandler: !!middleware.afterHandlerExecution,
        errorHandler: !!middleware.onHandlerError
      }
    });
  }

  // ============================================================================
  // SECTION 4: METRICS & UTILITY METHODS (Lines 361-400)
  // AI Context: "Performance tracking, metrics collection, and utility functions"
  // ============================================================================

  /**
   * Get middleware performance metrics
   */
  public getMiddlewareMetrics(): typeof this._middlewareMetrics & {
    middlewareCount: number;
    averageMiddlewareTime: number;
    reliabilityScore: number;
  } {
    const averageReliability = Array.from(this._reliabilityScores.values())
      .reduce((sum, score) => sum + score, 0) / this._reliabilityScores.size || 0;

    return {
      ...this._middlewareMetrics,
      middlewareCount: this._middleware.length,
      averageMiddlewareTime: this._middlewareMetrics.averageExecutionTime,
      reliabilityScore: averageReliability
    };
  }

  /**
   * Update middleware performance metrics
   */
  private _updateMiddlewarePerformanceMetrics(executionTime: number): void {
    const totalExecutions = this._middlewareMetrics.totalMiddlewareExecutions;
    const currentAverage = this._middlewareMetrics.averageExecutionTime;
    
    this._middlewareMetrics.averageExecutionTime = 
      (currentAverage * (totalExecutions - 1) + executionTime) / totalExecutions;
  }

  /**
   * ✅ RESILIENT TIMING: Enhanced error handling with timing context
   */
  private _enhanceErrorWithTimingContext(error: unknown, context: ResilientTimingContext): Error {
    const enhancedError = error instanceof Error ? error : new Error(String(error));
    
    Object.assign(enhancedError, {
      component: 'MiddlewareManager',
      timestamp: new Date().toISOString(),
      timingContext: 'resilient_timer_used'
    });
    
    return enhancedError;
  }

  /**
   * Clear all middleware (for testing or reset scenarios)
   */
  public clearAllMiddleware(): void {
    const count = this._middleware.length;
    this._middleware.length = 0;
    this._middlewarePerformanceCache.clear();
    this._reliabilityScores.clear();

    this.logInfo('All middleware cleared', { previousCount: count });
  }
} 