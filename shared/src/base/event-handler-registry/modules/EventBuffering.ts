/**
 * ============================================================================
 * AI CONTEXT: EventBuffering - Advanced Event Queuing and Buffering System
 * Purpose: Provides enterprise-grade event buffering with multiple strategies
 * Complexity: Moderate - Buffering algorithms with resilient timing integration
 * AI Navigation: 6 logical sections - Configuration, Buffering, Flushing, Monitoring, Retry, Utilities
 * Dependencies: MemorySafeResourceManager, AtomicCircularBufferEnhanced, ResilientTimer
 * Performance: <5ms buffer operations, >0.8 reliability score for timing measurements
 * ============================================================================
 */

/**
 * @file EventBuffering
 * @filepath shared/src/base/event-handler-registry/modules/EventBuffering.ts
 * @task-id M-TSK-01.SUB-01.1.ENH-02.MOD-04
 * @component event-buffering
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-event-buffering-system
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Module
 * @created 2025-07-27 14:48:54 +03
 * @modified 2025-07-27 14:48:54 +03
 *
 * @description
 * Enterprise-grade event buffering and queuing engine providing:
 * - Configurable buffering strategies (FIFO, LIFO, priority-based)
 * - Resilient timing integration for all flush and monitoring operations
 * - Comprehensive retry logic with exponential backoff
 * - Dead letter queue with comprehensive audit trails
 * - Performance optimization with <5ms buffer operations
 * - Complete error handling with timing context integration
 * - Memory-safe patterns following Enhanced services architecture
 * - Anti-Simplification Policy compliance with full feature implementation
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-002-event-emission-architecture
 * @governance-dcr DCR-foundation-002-event-emission-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @depends-on shared/src/base/AtomicCircularBufferEnhanced
 * @depends-on shared/src/base/utils/ResilientTiming
 * @depends-on shared/src/base/event-handler-registry/types/EventTypes
 * @enables shared/src/base/EventHandlerRegistryEnhanced
 * @related-contexts foundation-context, memory-safety-context, event-processing-context
 * @governance-impact framework-foundation, event-buffering, performance-optimization
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type memory-safety-enhanced-module
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @anti-simplification-compliant true
 * @documentation docs/contexts/memory-safety-context/modules/EventBuffering.md
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-27) - Initial extraction with resilient timing integration
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES (Lines 1-80)
// AI Context: "Enhanced buffering system dependencies and resilient timing imports"
// ============================================================================

import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { AtomicCircularBufferEnhanced } from '../../AtomicCircularBufferEnhanced';
import { SimpleLogger, ILoggingService } from '../../LoggingMixin';
import {
  ResilientTimer,
  createResilientTimer,
  IResilientTimingResult,
  IResilientTimingContext
} from '../../utils/ResilientTiming';
import {
  ResilientMetricsCollector,
  createResilientMetricsCollector,
  IResilientMetricsSnapshot
} from '../../utils/ResilientMetrics';
import {
  IEventBuffering,
  IBufferedEvent,
  IEmissionOptions,
  IEmissionResult,
  IRetryPolicy,
  IErrorClassification,
  IEventPriorityContext,
  IBufferingMetrics
} from '../types/EventTypes';

// ============================================================================
// SECTION 2: TYPE DEFINITIONS & INTERFACES (Lines 81-180)
// AI Context: "Buffering-specific types and configuration interfaces"
// ============================================================================

export interface IEventBufferingConfig {
  readonly buffering: IEventBuffering;
  readonly enableMetrics: boolean;
  readonly performanceThreshold: number; // Maximum buffer operation time in ms
  readonly enableResilientTiming: boolean;
  readonly monitoringInterval: number;
}

export interface IBufferOperationResult {
  success: boolean;
  bufferedEventId?: string;
  processingTime: number;
  timingReliability: number;
  bufferUtilization: number;
  autoFlushed: boolean;
}

export interface IFlushResult {
  eventsFlushed: number;
  successfulEvents: number;
  failedEvents: number;
  flushDuration: number;
  timingReliability: number;
  bufferSizeAfterFlush: number;
}

export interface IDeadLetterEvent {
  originalEvent: IBufferedEvent;
  error: {
    message: string;
    stack?: string;
    name: string;
  };
  reason: string;
  timestamp: Date;
  attempts: number;
}

// ============================================================================
// SECTION 3: EVENT BUFFERING ENGINE IMPLEMENTATION (Lines 181-400)
// AI Context: "Core buffering engine with resilient timing integration"
// ============================================================================

/**
 * Enterprise-grade event buffering and queuing engine
 * 
 * Provides comprehensive buffering strategies with resilient timing:
 * - FIFO/LIFO/Priority-based buffering strategies
 * - Automatic flush with configurable intervals and thresholds
 * - Comprehensive retry logic with exponential backoff
 * - Dead letter queue with audit trails
 * - Performance monitoring with resilient measurement
 */
export class EventBuffering extends MemorySafeResourceManager {
  private _config: IEventBufferingConfig;
  private _metrics: IBufferingMetrics;
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  
  // Buffer infrastructure
  private _eventBuffer?: AtomicCircularBufferEnhanced<IBufferedEvent>;
  private _flushTimerId?: string;
  private _bufferMonitorTimerId?: string;

  constructor(config: IEventBufferingConfig) {
    super({
      resourceLimits: {
        maxMemoryUsage: 100 * 1024 * 1024, // 100MB
        maxCacheSize: 20000
      }
    });

    this._config = config;
    this._metrics = {
      bufferedEvents: 0,
      flushedEvents: 0,
      failedFlushes: 0,
      averageFlushTime: 0,
      totalRetries: 0,
      deadLetterEvents: 0,
      bufferUtilization: 0,
      performanceViolations: 0
    };
  }

  // ============================================================================
  // SECTION 4: INITIALIZATION & LIFECYCLE (Lines 401-550)
  // AI Context: "Module initialization with resilient timing infrastructure"
  // ============================================================================

  /**
   * Initialize the buffering engine with resilient timing
   */
  protected async doInitialize(): Promise<void> {
    await super.doInitialize();

    // ✅ RESILIENT: Initialize timing infrastructure
    this._resilientTimer = createResilientTimer({
      fallbackStrategy: 'intelligent_estimate',
      environmentOptimized: true,
      performanceTarget: 'enterprise',
      enableDetailedLogging: process.env.NODE_ENV !== 'production'
    });

    this._metricsCollector = createResilientMetricsCollector({
      enableCaching: true,
      maxCacheSize: 3000,
      fallbackEnabled: true,
      aggregationStrategy: 'statistical',
      retentionPolicy: 'component_lifecycle'
    });

    // Initialize buffering if enabled
    if (this._config.buffering.enabled) {
      await this._initializeEventBuffering();
    }

    this.logInfo('EventBuffering initialized', {
      enabled: this._config.buffering.enabled,
      bufferSize: this._config.buffering.bufferSize,
      strategy: this._config.buffering.bufferStrategy,
      enableMetrics: this._config.enableMetrics,
      resilientTimingEnabled: this._config.enableResilientTiming
    });
  }

  /**
   * Shutdown with proper cleanup
   */
  protected async doShutdown(): Promise<void> {
    try {
      // Clear timers first
      if (this._flushTimerId) {
        this.clearSafeInterval(this._flushTimerId);
      }
      if (this._bufferMonitorTimerId) {
        this.clearSafeInterval(this._bufferMonitorTimerId);
      }

      // Flush remaining events
      if (this._eventBuffer && this._eventBuffer.getSize() > 0) {
        await this._performEnterpriseEventFlush();
      }

      // Cleanup resilient timing
      if (this._resilientTimer) {
        await this._resilientTimer.cleanup();
      }
      if (this._metricsCollector) {
        await this._metricsCollector.shutdown();
      }
    } catch (error) {
      this.logError('Error during EventBuffering shutdown', error);
    } finally {
      await super.doShutdown();
    }
  }

  // ============================================================================
  // SECTION 5: CORE BUFFERING OPERATIONS (Lines 551-750)
  // AI Context: "Main buffering logic with resilient timing measurement"
  // ============================================================================

  /**
   * Add event to buffer with enterprise handling
   * 
   * ✅ RESILIENT: Replaces vulnerable timing pattern #1
   * ❌ OLD: const startTime = performance.now();
   * ✅ NEW: ResilientTimer context measurement
   */
  public async bufferEvent(
    eventType: string,
    data: unknown,
    options: IEmissionOptions = {}
  ): Promise<IBufferOperationResult> {
    // ✅ RESILIENT: Create timing context for buffer operation
    const bufferContext = this._resilientTimer.createTimingContext('buffer-operation');

    try {
      if (!this._config.buffering.enabled) {
        throw new Error('Event buffering is not enabled');
      }

      if (!this._eventBuffer) {
        throw new Error('Event buffer not initialized');
      }

      // Create buffered event
      const bufferedEvent: IBufferedEvent = {
        id: this._generateEventId(),
        type: eventType,
        data,
        options: this._validateAndEnhanceEmissionOptions(options),
        timestamp: new Date(),
        priority: this._calculateEventPriority(eventType, data, options),
        retryCount: 0,
        metadata: this._generateEventMetadata(eventType, data, options)
      };

      // Add to buffer with overflow handling
      await this._addToBufferWithEnterpriseHandling(bufferedEvent);
      this._metrics.bufferedEvents++;

      const timingResult = bufferContext.complete();

      // ✅ RESILIENT: Record timing with reliability information
      if (this._config.enableMetrics) {
        await this._metricsCollector.recordTiming('buffer_operation', timingResult);
      }

      // Check if we should auto-flush
      const shouldFlush = this._shouldAutoFlush();
      let autoFlushed = false;
      
      if (shouldFlush) {
        await this._performEnterpriseEventFlush();
        autoFlushed = true;
      }

      const result: IBufferOperationResult = {
        success: true,
        bufferedEventId: bufferedEvent.id,
        processingTime: timingResult.isReliable ? timingResult.duration : timingResult.estimatedDuration,
        timingReliability: timingResult.confidence,
        bufferUtilization: this._eventBuffer.getSize() / this._config.buffering.bufferSize,
        autoFlushed
      };

      // Check performance threshold
      if (result.processingTime > this._config.performanceThreshold) {
        this._metrics.performanceViolations++;
        this.logWarning('Buffer operation performance threshold exceeded', {
          processingTime: result.processingTime,
          threshold: this._config.performanceThreshold,
          eventType
        });
      }

      this.logDebug('Event buffered successfully', {
        eventId: bufferedEvent.id,
        eventType,
        processingTime: result.processingTime,
        bufferUtilization: result.bufferUtilization,
        autoFlushed: result.autoFlushed
      });

      return result;

    } catch (error) {
      bufferContext.fail();
      throw this._enhanceErrorWithTimingContext(error, bufferContext);
    }
  }

  /**
   * Perform enterprise event flush
   * 
   * ✅ RESILIENT: Replaces vulnerable timing pattern #2
   * ❌ OLD: const flushStartTime = performance.now();
   * ✅ NEW: ResilientTimer context measurement
   */
  public async flushBuffer(): Promise<IFlushResult> {
    // ✅ RESILIENT: Create timing context for flush operation
    const flushContext = this._resilientTimer.createTimingContext('buffer-flush');

    try {
      if (!this._eventBuffer || !this._config.buffering.enabled) {
        const emptyResult = {
          eventsFlushed: 0,
          successfulEvents: 0,
          failedEvents: 0,
          flushDuration: 0,
          timingReliability: 1.0,
          bufferSizeAfterFlush: 0
        };
        flushContext.complete();
        return emptyResult;
      }

      const bufferedEvents = this._eventBuffer.getAllItems();
      
      if (bufferedEvents.size === 0) {
        const timingResult = flushContext.complete();
        this._updateFlushMetrics(0, 0, timingResult);
        return {
          eventsFlushed: 0,
          successfulEvents: 0,
          failedEvents: 0,
          flushDuration: timingResult.isReliable ? timingResult.duration : timingResult.estimatedDuration,
          timingReliability: timingResult.confidence,
          bufferSizeAfterFlush: 0
        };
      }

      // Sort events based on enterprise strategy
      const eventsToFlush = this._sortBufferedEventsEnterprise(Array.from(bufferedEvents.values()));
      
      let successCount = 0;
      let failureCount = 0;
      const batchResults: IEmissionResult[] = [];

      // Process events with enterprise-grade error handling and monitoring
      for (const event of eventsToFlush) {
        try {
          const result = await this._executeEventWithEnterpriseMonitoring(event);
          batchResults.push(result);
          
          if (result.failedHandlers === 0) {
            successCount++;
          } else {
            failureCount++;
          }
        } catch (error) {
          failureCount++;
          await this._handleBufferedEventError(event, error);
        }
      }

      // Clear processed events with atomic operations
      await this._clearProcessedEventsAtomically(eventsToFlush);
      
      const timingResult = flushContext.complete();
      this._updateFlushMetrics(successCount, failureCount, timingResult);

      // ✅ RESILIENT: Record flush timing
      if (this._config.enableMetrics) {
        await this._metricsCollector.recordTiming('buffer_flush', timingResult);
      }

      const result: IFlushResult = {
        eventsFlushed: eventsToFlush.length,
        successfulEvents: successCount,
        failedEvents: failureCount,
        flushDuration: timingResult.isReliable ? timingResult.duration : timingResult.estimatedDuration,
        timingReliability: timingResult.confidence,
        bufferSizeAfterFlush: this._eventBuffer.getSize()
      };

      this.logInfo('Enterprise event buffer flush completed', {
        eventsProcessed: result.eventsFlushed,
        successfulEvents: result.successfulEvents,
        failedEvents: result.failedEvents,
        flushDuration: result.flushDuration,
        bufferSizeAfterFlush: result.bufferSizeAfterFlush
      });

      return result;

    } catch (error) {
      flushContext.fail();
      throw this._enhanceErrorWithTimingContext(error, flushContext);
    }
  }

  // ============================================================================
  // SECTION 6: BUFFER MONITORING & HEALTH (Lines 751-900)
  // AI Context: "Buffer monitoring with resilient timing measurement"
  // ============================================================================

  /**
   * Monitor buffer health and utilization
   * 
   * ✅ RESILIENT: Replaces vulnerable timing pattern #3
   * ❌ OLD: timestamp: new Date(Date.now() + delayMs)
   * ✅ NEW: Resilient timestamp generation for retry scheduling
   */
  private _monitorBufferHealth(): void {
    if (!this._eventBuffer || !this._config.buffering) return;

    const currentSize = this._eventBuffer.getSize();
    const maxSize = this._config.buffering.bufferSize;
    const utilizationRate = currentSize / maxSize;
    
    // Update metrics
    this._metrics.bufferUtilization = utilizationRate;
    
    // Enterprise monitoring with predictive alerting
    if (utilizationRate > 0.8) {
      this.logWarning('Event buffer utilization high', {
        currentSize,
        maxSize,
        utilizationRate: (utilizationRate * 100).toFixed(2) + '%',
        strategy: this._config.buffering.bufferStrategy
      });
      
      // Proactive flush if near capacity
      if (utilizationRate > 0.9) {
        this.logInfo('Triggering proactive buffer flush due to high utilization');
        this._performEnterpriseEventFlush().catch(error => {
          this.logError('Proactive buffer flush failed', error);
        });
      }
    }
  }

  /**
   * Schedule event retry with resilient timing
   */
  private async _scheduleEventRetry(event: IBufferedEvent, delayMs: number): Promise<void> {
    // ✅ RESILIENT: Use resilient timing for retry scheduling
    const futureTimestamp = this._resilientTimer.getCurrentTimestamp() + delayMs;
    
    const retryEvent: IBufferedEvent = {
      ...event,
      id: `${event.id}_retry_${event.retryCount}`,
      timestamp: new Date(futureTimestamp),
      priority: Math.max(event.priority - 1, 1) // Reduce priority for retries
    };
    
    // Schedule using enterprise timer coordination
    this.createSafeTimeout(
      async () => {
        try {
          await this._eventBuffer?.addItem(retryEvent.id, retryEvent);
          this.logDebug('Retry event added to buffer', { eventId: retryEvent.id });
        } catch (error) {
          this.logError('Failed to schedule retry event', error, { originalEventId: event.id });
          await this._moveToDeadLetterQueue(event, error, 'retry_scheduling_failed');
        }
      },
      delayMs,
      `event-retry-${retryEvent.id}`
    );
  }

  // ============================================================================
  // SECTION 7: RETRY LOGIC & ERROR HANDLING (Lines 901-1100)
  // AI Context: "Comprehensive retry and error handling with timing context"
  // ============================================================================

  /**
   * Handle buffered event error with enterprise retry logic
   */
  private async _handleBufferedEventError(event: IBufferedEvent, error: unknown): Promise<void> {
    event.retryCount++;
    
    const retryPolicy = event.options.retryPolicy || this._getDefaultRetryPolicy();
    
    // Enterprise-grade error classification
    const errorType = this._classifyError(error);
    const shouldRetry = this._shouldRetryBasedOnError(errorType, event.retryCount, retryPolicy);
    
    if (shouldRetry && event.retryCount <= retryPolicy.maxRetries) {
      // Calculate exponential backoff with jitter
      const backoffDelay = this._calculateExponentialBackoff(
        event.retryCount, 
        retryPolicy.retryDelayMs, 
        retryPolicy.backoffMultiplier,
        retryPolicy.maxBackoffDelayMs
      );
      
      // Schedule retry with enterprise monitoring
      await this._scheduleEventRetry(event, backoffDelay);
      
      this._metrics.totalRetries++;
      this.logInfo('Event scheduled for retry', {
        eventId: event.id,
        retryCount: event.retryCount,
        backoffDelay,
        errorType: errorType.category
      });
    } else {
      // Move to dead letter queue with comprehensive audit trail
      await this._moveToDeadLetterQueue(event, error, 'max_retries_exceeded');
      
      this._metrics.deadLetterEvents++;
      this.logError('Event moved to dead letter queue', error, {
        eventId: event.id,
        finalRetryCount: event.retryCount,
        reason: event.retryCount > retryPolicy.maxRetries ? 'max_retries_exceeded' : 'non_retryable_error'
      });
    }
  }

  /**
   * Move event to dead letter queue
   */
  private async _moveToDeadLetterQueue(
    event: IBufferedEvent, 
    error: unknown, 
    reason: string
  ): Promise<void> {
    const dlqEvent: IDeadLetterEvent = {
      originalEvent: event,
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : { message: String(error), name: 'UnknownError' },
      reason,
      timestamp: new Date(),
      attempts: event.retryCount
    };
    
    // Emit dead letter queue event for external monitoring
    this.emit('deadLetterEvent', dlqEvent);
    
    // Store in persistent dead letter queue if configured
    if (this._config.buffering.deadLetterQueueHandler) {
      await this._config.buffering.deadLetterQueueHandler(dlqEvent);
    }
  }

  // ============================================================================
  // SECTION 8: UTILITY METHODS & HELPERS (Lines 1101-1300)
  // AI Context: "Helper methods and enterprise utility functions"
  // ============================================================================

  /**
   * Initialize event buffering infrastructure
   */
  private async _initializeEventBuffering(): Promise<void> {
    if (!this._config.buffering) return;

    // Create buffer using AtomicCircularBufferEnhanced from Phase 1
    this._eventBuffer = new AtomicCircularBufferEnhanced<IBufferedEvent>(
      this._config.buffering.bufferSize,
      {
        evictionPolicy: this._config.buffering.bufferStrategy === 'fifo' ? 'fifo' : 'lru',
        autoCompaction: true,
        compactionThreshold: 0.3
      }
    );

    // Initialize buffer and await proper initialization
    await this._eventBuffer.initialize();

    // Set up flush timer
    if (this._config.buffering.flushInterval > 0) {
      this._flushTimerId = this.createSafeInterval(
        () => this._performEnterpriseEventFlush(),
        this._config.buffering.flushInterval,
        'enhanced-event-buffer-flush'
      );
    }

    // Initialize buffer monitoring timer
    this._bufferMonitorTimerId = this.createSafeInterval(
      () => this._monitorBufferHealth(),
      this._config.monitoringInterval,
      'buffer-health-monitor'
    );
  }

  /**
   * Get current buffering metrics
   */
  public getMetrics(): IBufferingMetrics {
    return { ...this._metrics };
  }

  /**
   * Get resilient metrics snapshot
   */
  public async getResilientMetricsSnapshot(): Promise<IResilientMetricsSnapshot | null> {
    if (!this._config.enableResilientTiming || !this._metricsCollector) {
      return null;
    }

    return await this._metricsCollector.getSnapshot();
  }

  /**
   * Enhance error with timing context information
   */
  private _enhanceErrorWithTimingContext(error: unknown, context: IResilientTimingContext): Error {
    const enhancedError = error instanceof Error ? error : new Error(String(error));
    
    Object.assign(enhancedError, {
      resilientContext: context.getContext(),
      timingData: context.getSummary(),
      component: 'EventBuffering',
      timestamp: new Date().toISOString(),
      bufferSize: this._eventBuffer?.getSize() || 0
    });
    
    return enhancedError;
  }

  // Placeholder methods that would delegate to the main emission system
  private async _performEnterpriseEventFlush(): Promise<void> {
    await this.flushBuffer();
  }

  private _generateEventId(): string {
    return `evt_${this._resilientTimer.getCurrentTimestamp()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private _validateAndEnhanceEmissionOptions(options: IEmissionOptions): IEmissionOptions {
    return { ...options };
  }

  private _generateEventMetadata(eventType: string, data: unknown, options: IEmissionOptions): Record<string, unknown> {
    return {
      eventType,
      bufferedAt: new Date().toISOString(),
      priority: options.priority || 'normal'
    };
  }

  private _calculateEventPriority(eventType: string, data: unknown, options: IEmissionOptions): number {
    const priorityMap = { critical: 4, high: 3, normal: 2, low: 1 };
    return priorityMap[options.priority || 'normal'] || 2;
  }

  private _shouldAutoFlush(): boolean {
    if (!this._eventBuffer || !this._config.buffering.autoFlushThreshold) return false;
    return this._eventBuffer.getSize() >= this._config.buffering.autoFlushThreshold;
  }

  private async _addToBufferWithEnterpriseHandling(event: IBufferedEvent): Promise<void> {
    if (!this._eventBuffer) throw new Error('Buffer not initialized');
    await this._eventBuffer.addItem(event.id, event);
  }

  private _sortBufferedEventsEnterprise(events: IBufferedEvent[]): IBufferedEvent[] {
    return events.sort((a, b) => b.priority - a.priority || a.timestamp.getTime() - b.timestamp.getTime());
  }

  private async _executeEventWithEnterpriseMonitoring(event: IBufferedEvent): Promise<IEmissionResult> {
    // This would delegate to the main emission system
    return {
      eventId: event.id,
      eventType: event.type,
      targetHandlers: 0,
      successfulHandlers: 0,
      failedHandlers: 0,
      executionTime: 0,
      handlerResults: [],
      errors: []
    };
  }

  private async _clearProcessedEventsAtomically(events: IBufferedEvent[]): Promise<void> {
    if (!this._eventBuffer) return;
    for (const event of events) {
      this._eventBuffer.removeItem(event.id);
    }
  }

  private _updateFlushMetrics(successCount: number, failureCount: number, timingResult: IResilientTimingResult): void {
    this._metrics.flushedEvents += successCount;
    this._metrics.failedFlushes += failureCount;
    
    const flushTime = timingResult.isReliable ? timingResult.duration : timingResult.estimatedDuration;
    this._metrics.averageFlushTime = 
      (this._metrics.averageFlushTime + flushTime) / 2;
  }

  private _getDefaultRetryPolicy(): IRetryPolicy {
    return {
      maxRetries: 3,
      retryDelayMs: 1000,
      backoffMultiplier: 2.0,
      maxBackoffDelayMs: 30000,
      retryableErrorTypes: ['network', 'timeout', 'service_unavailable', 'rate_limit'],
      nonRetryableErrorTypes: ['authentication', 'authorization', 'validation', 'malformed_data']
    };
  }

  private _classifyError(error: unknown): IErrorClassification {
    const errorMessage = error instanceof Error ? error.message.toLowerCase() : String(error).toLowerCase();
    
    if (errorMessage.includes('timeout') || errorMessage.includes('etimedout')) {
      return { category: 'timeout', severity: 'medium', retryable: true };
    }
    if (errorMessage.includes('network') || errorMessage.includes('econnrefused')) {
      return { category: 'network', severity: 'medium', retryable: true };
    }
    if (errorMessage.includes('rate limit') || errorMessage.includes('429')) {
      return { category: 'rate_limit', severity: 'low', retryable: true };
    }
    if (errorMessage.includes('unauthorized') || errorMessage.includes('401')) {
      return { category: 'authentication', severity: 'high', retryable: false };
    }
    if (errorMessage.includes('forbidden') || errorMessage.includes('403')) {
      return { category: 'authorization', severity: 'high', retryable: false };
    }
    if (errorMessage.includes('validation') || errorMessage.includes('400')) {
      return { category: 'validation', severity: 'high', retryable: false };
    }
    
    return { category: 'unknown', severity: 'medium', retryable: true };
  }

  private _shouldRetryBasedOnError(
    errorType: IErrorClassification, 
    retryCount: number, 
    retryPolicy: IRetryPolicy
  ): boolean {
    if (!errorType.retryable) return false;
    if (retryCount >= retryPolicy.maxRetries) return false;
    if (retryPolicy.nonRetryableErrorTypes?.includes(errorType.category)) return false;
    
    return retryPolicy.retryableErrorTypes?.includes(errorType.category) ?? true;
  }

  private _calculateExponentialBackoff(
    retryCount: number, 
    baseDelay: number, 
    multiplier: number,
    maxDelay: number = 30000
  ): number {
    const exponentialDelay = baseDelay * Math.pow(multiplier, retryCount - 1);
    const jitter = Math.random() * 0.1 * exponentialDelay;
    return Math.min(exponentialDelay + jitter, maxDelay);
  }
} 