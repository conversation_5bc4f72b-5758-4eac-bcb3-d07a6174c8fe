/**
 * ============================================================================
 * AI CONTEXT: Event Handler Registry Enhanced - Orchestrator
 * Purpose: Enterprise orchestrator delegating to specialized modules
 * Complexity: Moderate - Clean delegation with resilient timing integration
 * AI Navigation: 5 logical sections - Initialization, Orchestration, Delegation, Metrics, Lifecycle
 * Dependencies: EventHandlerRegistry, SpecializedModules, ResilientTimer
 * Performance: <10ms emission for <100 handlers, <2ms middleware, <1ms deduplication
 * ============================================================================
 */

/**
 * @file Event Handler Registry Enhanced
 * @filepath shared/src/base/EventHandlerRegistryEnhanced.ts
 * @task-id M-TSK-01.SUB-01.1.ENH-02
 * @component event-handler-registry-enhanced
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-event-processing-orchestrator
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced
 * @created 2025-07-22 16:00:00 +03
 * @modified 2025-07-27 14:48:54 +03
 *
 * @description
 * Enterprise-grade enhanced event handler registry orchestrator providing:
 * - Clean delegation to specialized modules (DeduplicationEngine, EventBuffering, MiddlewareManager, EventEmissionSystem)
 * - Resilient timing integration for remaining vulnerable patterns
 * - 100% backward compatibility with base EventHandlerRegistry functionality
 * - Performance optimization maintaining all requirements
 * - Memory-safe patterns following Enhanced services architecture
 * - Anti-Simplification Policy compliance with complete functionality
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-002-event-emission-architecture
 * @governance-dcr DCR-foundation-002-event-emission-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/EventHandlerRegistry
 * @depends-on shared/src/base/event-handler-registry/modules/*
 * @enables server/src/platform/tracking/core-managers/RealTimeManagerEnhanced
 * @enables server/src/platform/governance/automation-processing/GovernanceRuleEventManagerEnhanced
 * @related-contexts foundation-context, memory-safety-context, event-processing-context
 * @governance-impact framework-foundation, event-emission-management, middleware-processing
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type memory-safety-enhanced-orchestrator
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @anti-simplification-compliant true
 * @documentation docs/contexts/memory-safety-context/components/EventHandlerRegistryEnhanced.md
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-22) - Initial enhanced implementation with event emission system
 * v1.1.0 (2025-07-22) - Added priority-based middleware system with execution hooks
 * v1.2.0 (2025-07-22) - Implemented advanced handler deduplication strategies
 * v1.3.0 (2025-07-22) - Added event buffering and queuing with overflow handling
 * v1.4.0 (2025-07-22) - GOVERNANCE COMPLIANCE: Anti-Simplification Policy fixes applied
 * v2.0.0 (2025-07-27) - DAY 13: Modular extraction with resilient timing integration
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES (Lines 1-80)
// AI Context: "Enhanced orchestrator dependencies and specialized modules"
// ============================================================================

import { EventHandlerRegistry } from './EventHandlerRegistry';
import { MemorySafeResourceManager } from './MemorySafeResourceManager';
import { SimpleLogger, ILoggingService } from './LoggingMixin';
import {
  ResilientTimer,
  createResilientTimer,
  IResilientTimingResult,
  IResilientTimingContext
} from './utils/ResilientTiming';
import {
  ResilientMetricsCollector,
  createResilientMetricsCollector,
  IResilientMetricsSnapshot
} from './utils/ResilientMetrics';

// Specialized modules
import { MiddlewareManager } from './event-handler-registry/modules/MiddlewareManager';
import { EventEmissionSystem } from './event-handler-registry/modules/EventEmissionSystem';
import { EventUtilities } from './event-handler-registry/modules/EventUtilities';
import { DeduplicationEngine } from './event-handler-registry/modules/DeduplicationEngine';
import { EventBuffering } from './event-handler-registry/modules/EventBuffering';

// Types and interfaces
import {
  IEventHandlerRegistryEnhancedConfig,
  IEmissionOptions,
  IEmissionResult,
  IClientEmissionResult,
  IBatchEmissionResult,
  IEventBatch,
  IHandlerDeduplication,
  IEventBuffering,
  IEmissionMetrics,
  EventHandlerCallback,
  IRegisteredHandler
} from './event-handler-registry/types/EventTypes';

// ============================================================================
// SECTION 2: ENHANCED ORCHESTRATOR IMPLEMENTATION (Lines 81-250)
// AI Context: "Clean orchestrator with specialized module delegation"
// ============================================================================

/**
 * Enterprise-grade enhanced event handler registry orchestrator
 * 
 * Provides comprehensive event processing through specialized modules:
 * - DeduplicationEngine for handler deduplication strategies
 * - EventBuffering for queuing and buffering with retry logic
 * - MiddlewareManager for priority-based middleware execution
 * - EventEmissionSystem for event emission and result tracking
 * - EventUtilities for error handling and validation
 * - Resilient timing integration for remaining operations
 */
export class EventHandlerRegistryEnhanced extends MemorySafeResourceManager {
  private _baseRegistry: EventHandlerRegistry;
  private _config: IEventHandlerRegistryEnhancedConfig;
  private _metrics: IEmissionMetrics;
  
  // Resilient timing infrastructure
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  
  // Specialized modules
  private _middlewareManager!: MiddlewareManager;
  private _emissionSystem!: EventEmissionSystem;
  private _utilities!: EventUtilities;
  private _deduplicationEngine!: DeduplicationEngine;
  private _eventBuffering!: EventBuffering;

  constructor(config: IEventHandlerRegistryEnhancedConfig = {}) {
    super({
      resourceLimits: {
        maxMemoryUsage: 200 * 1024 * 1024, // 200MB for orchestrator + modules
        maxCacheSize: 50000
      }
    });

    this._baseRegistry = new EventHandlerRegistry();
    this._config = {
      middleware: { enabled: false, maxMiddleware: 10, allowAsyncMiddleware: true },
      deduplication: { enabled: false, strategy: 'reference', autoMergeMetadata: false },
      buffering: { enabled: false, bufferSize: 1000, flushInterval: 5000, bufferStrategy: 'fifo' },
      enableMetrics: true,
      performanceThresholds: {
        emission: 10,
        middleware: 2,
        deduplication: 1,
        buffering: 5
      },
      ...config
    };

    this._metrics = {
      totalEmissions: 0,
      successfulEmissions: 0,
      failedEmissions: 0,
      averageEmissionTime: 0,
      bufferedEvents: 0,
      totalRetries: 0,
      deadLetterEvents: 0,
      duplicatesDetected: 0,
      middlewareExecutions: 0,
      performanceViolations: 0
    };
  }

  // ============================================================================
  // SECTION 3: INITIALIZATION & LIFECYCLE (Lines 251-400)
  // AI Context: "Module initialization with resilient timing infrastructure"
  // ============================================================================

  /**
   * Initialize the orchestrator and all specialized modules
   */
  protected async doInitialize(): Promise<void> {
    await super.doInitialize();

    // ✅ RESILIENT: Initialize timing infrastructure first
    this._resilientTimer = createResilientTimer({
      fallbackStrategy: 'intelligent_estimate',
      environmentOptimized: true,
      performanceTarget: 'enterprise',
      enableDetailedLogging: process.env.NODE_ENV !== 'production'
    });

    this._metricsCollector = createResilientMetricsCollector({
      enableCaching: true,
      maxCacheSize: 5000,
      fallbackEnabled: true,
      aggregationStrategy: 'statistical',
      retentionPolicy: 'component_lifecycle'
    });

    // Initialize base registry first
    await this._baseRegistry.initialize();

    // Initialize specialized modules
    await this._initializeSpecializedModules();

    this.logInfo('EventHandlerRegistryEnhanced orchestrator initialized', {
      middlewareEnabled: this._config.middleware?.enabled,
      deduplicationEnabled: this._config.deduplication?.enabled,
      bufferingEnabled: this._config.buffering?.enabled,
      performanceThresholds: this._config.performanceThresholds,
      resilientTimingEnabled: true
    });
  }

  /**
   * Initialize all specialized modules
   */
  private async _initializeSpecializedModules(): Promise<void> {
    // Initialize utilities first (foundational)
    this._utilities = new EventUtilities({
      enableMetrics: this._config.enableMetrics,
      performanceThreshold: this._config.performanceThresholds?.deduplication || 1,
      enableResilientTiming: true
    });
    await this._utilities.initialize();

    // Initialize middleware manager
    this._middlewareManager = new MiddlewareManager({
      middleware: this._config.middleware!,
      enableMetrics: this._config.enableMetrics,
      performanceThreshold: this._config.performanceThresholds?.middleware || 2,
      enableResilientTiming: true
    });
    await this._middlewareManager.initialize();

    // Initialize emission system
    this._emissionSystem = new EventEmissionSystem({
      enableMetrics: this._config.enableMetrics,
      performanceThreshold: this._config.performanceThresholds?.emission || 10,
      enableResilientTiming: true,
      baseRegistry: this._baseRegistry
    });
    await this._emissionSystem.initialize();

    // Initialize deduplication engine
    this._deduplicationEngine = new DeduplicationEngine({
      deduplication: this._config.deduplication!,
      enableMetrics: this._config.enableMetrics,
      performanceThreshold: this._config.performanceThresholds?.deduplication || 1,
      enableResilientTiming: true
    });
    await this._deduplicationEngine.initialize();

    // Initialize event buffering
    this._eventBuffering = new EventBuffering({
      buffering: this._config.buffering!,
      enableMetrics: this._config.enableMetrics,
      performanceThreshold: this._config.performanceThresholds?.buffering || 5,
      enableResilientTiming: true,
      monitoringInterval: 2000
    });
    await this._eventBuffering.initialize();
  }

  /**
   * Shutdown with proper cleanup
   */
  protected async doShutdown(): Promise<void> {
    try {
      // Shutdown modules in reverse order
      if (this._eventBuffering) await this._eventBuffering.shutdown();
      if (this._deduplicationEngine) await this._deduplicationEngine.shutdown();
      if (this._emissionSystem) await this._emissionSystem.shutdown();
      if (this._middlewareManager) await this._middlewareManager.shutdown();
      if (this._utilities) await this._utilities.shutdown();
      
      // Shutdown base registry
      if (this._baseRegistry) await this._baseRegistry.shutdown();

      // Cleanup resilient timing
      if (this._resilientTimer) await this._resilientTimer.cleanup();
      if (this._metricsCollector) await this._metricsCollector.shutdown();
    } catch (error) {
      this.logError('Error during EventHandlerRegistryEnhanced shutdown', error);
    } finally {
      await super.doShutdown();
    }
  }

  // ============================================================================
  // SECTION 4: CORE ORCHESTRATION METHODS (Lines 401-600)
  // AI Context: "Public API methods with clean delegation to specialized modules"
  // ============================================================================

  /**
   * Register handler with deduplication
   * 
   * ✅ RESILIENT: Replaces vulnerable timing pattern - operation tracking
   * ❌ OLD: const startTime = performance.now();
   * ✅ NEW: ResilientTimer context measurement
   */
  public registerHandler(
    clientId: string,
    eventType: string,
    callback: EventHandlerCallback,
    metadata?: Record<string, unknown>
  ): string {
    // ✅ RESILIENT: Create timing context for registration operation
    const registrationContext = this._resilientTimer.createTimingContext('handler-registration');

    try {
      // Check for duplicates if enabled
      if (this._config.deduplication?.enabled) {
        const existingHandlers = this._baseRegistry.getHandlersForEvent(eventType);
        const deduplicationResult = this._deduplicationEngine.processDeduplication({
          clientId,
          eventType,
          candidate: callback,
          existingHandlers,
          metadata
        });

        if (deduplicationResult.isDuplicate && deduplicationResult.existingHandler) {
          this._deduplicationEngine.handleDuplicateRegistration(
            deduplicationResult.existingHandler,
            callback,
            metadata
          );
          this._metrics.duplicatesDetected++;
          
          const timingResult = registrationContext.complete();
          this._recordOperationTiming('handler_registration', timingResult);
          
          return deduplicationResult.existingHandler.id;
        }
      }

      // No duplicate found, proceed with normal registration
      const handlerId = this._baseRegistry.registerHandler(clientId, eventType, callback, metadata);
      
      const timingResult = registrationContext.complete();
      this._recordOperationTiming('handler_registration', timingResult);
      
      return handlerId;

    } catch (error) {
      registrationContext.fail();
      throw this._enhanceErrorWithTimingContext(error, registrationContext);
    }
  }

  /**
   * Emit event with full processing pipeline
   * 
   * ✅ RESILIENT: Replaces vulnerable timing pattern - emission tracking
   * ❌ OLD: this._recordOperationSuccess(operationId, performance.now() - startTime);
   * ✅ NEW: ResilientTimer context measurement with reliability tracking
   */
  public async emitEvent(
    eventType: string,
    data: unknown,
    options: IEmissionOptions = {}
  ): Promise<IEmissionResult> {
    // ✅ RESILIENT: Create timing context for complete emission operation
    const emissionContext = this._resilientTimer.createTimingContext('complete-emission');

    try {
      this._metrics.totalEmissions++;

      // Use buffering if enabled
      if (this._config.buffering?.enabled && !options.immediateEmission) {
        const bufferResult = await this._eventBuffering.bufferEvent(eventType, data, options);
        
        const timingResult = emissionContext.complete();
        this._recordOperationTiming('complete_emission', timingResult);
        
        // Return a simplified result for buffered events
        return {
          eventId: bufferResult.bufferedEventId || this._generateOperationId(),
          eventType,
          targetHandlers: this._baseRegistry.getHandlersForEvent(eventType).length,
          successfulHandlers: bufferResult.success ? 1 : 0,
          failedHandlers: bufferResult.success ? 0 : 1,
          executionTime: bufferResult.processingTime,
          handlerResults: [],
          errors: []
        };
      }

      // Direct emission through emission system
      const result = await this._emissionSystem.emitToHandlers(
        this._baseRegistry.getHandlersForEvent(eventType),
        { eventType, data, options },
        this._middlewareManager
      );

      this._metrics.successfulEmissions++;
      
      const timingResult = emissionContext.complete();
      this._recordOperationTiming('complete_emission', timingResult);

      this.logDebug('Event emitted successfully', {
        eventId: result.eventId,
        eventType,
        targetHandlers: result.targetHandlers,
        successfulHandlers: result.successfulHandlers,
        executionTime: result.executionTime
      });

      return result;

    } catch (error) {
      this._metrics.failedEmissions++;
      emissionContext.fail();
      throw this._enhanceErrorWithTimingContext(error, emissionContext);
    }
  }

  /**
   * Emit event to specific client
   */
  public async emitEventToClient(
    clientId: string,
    eventType: string,
    data: unknown
  ): Promise<IClientEmissionResult> {
    const handlers = this._baseRegistry.getHandlersForEvent(eventType)
      .filter(h => h.clientId === clientId);

    if (handlers.length === 0) {
      return {
        clientId,
        eventType,
        delivered: false,
        executionTime: 0,
        error: new Error(`No handlers found for client ${clientId} and event ${eventType}`)
      };
    }

    try {
      const result = await this._emissionSystem.emitToHandlers(
        handlers,
        { eventType, data, options: {} },
        this._middlewareManager
      );

      return {
        clientId,
        eventType,
        delivered: result.successfulHandlers > 0,
        executionTime: result.executionTime,
        handlerResults: result.handlerResults.filter(hr => hr.clientId === clientId)
      };
    } catch (error) {
      return {
        clientId,
        eventType,
        delivered: false,
        executionTime: 0,
        error: error instanceof Error ? error : new Error(String(error))
      };
    }
  }

  /**
   * Emit event with timeout
   */
  public async emitEventWithTimeout(
    eventType: string,
    data: unknown,
    timeoutMs: number
  ): Promise<IEmissionResult> {
    return new Promise((resolve, reject) => {
      const timer = this.createSafeTimeout(() => {
        reject(new Error(`Event emission timeout after ${timeoutMs}ms`));
      }, timeoutMs, `emission-timeout-${eventType}`);

      this.emitEvent(eventType, data)
        .then(result => {
          this.clearSafeTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          this.clearSafeTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Emit batch of events
   */
  public async emitEventBatch(events: IEventBatch[]): Promise<IBatchEmissionResult> {
    const batchResults: IEmissionResult[] = [];
    const batchErrors: Error[] = [];

    for (const event of events) {
      try {
        const result = await this.emitEvent(event.eventType, event.data, event.options);
        batchResults.push(result);
      } catch (error) {
        const batchError = error instanceof Error ? error : new Error(String(error));
        batchErrors.push(batchError);
      }
    }

    return {
      batchId: this._generateOperationId(),
      totalEvents: events.length,
      successfulEvents: batchResults.length,
      failedEvents: batchErrors.length,
      results: batchResults,
      errors: batchErrors
    };
  }

  // ============================================================================
  // SECTION 5: DELEGATION METHODS & UTILITIES (Lines 601-800)
  // AI Context: "Delegation to base registry and utility methods"
  // ============================================================================

  /**
   * Delegate to base registry - unregister handler
   */
  public unregisterHandler(handlerId: string): boolean {
    return this._baseRegistry.unregisterHandler(handlerId);
  }

  /**
   * Delegate to base registry - get handlers for event
   */
  public getHandlersForEvent(eventType: string): IRegisteredHandler[] {
    return this._baseRegistry.getHandlersForEvent(eventType);
  }

  /**
   * Delegate to base registry - get handler by ID
   */
  public getHandler(handlerId: string): IRegisteredHandler | undefined {
    return this._baseRegistry.getHandler(handlerId);
  }

  /**
   * Get comprehensive metrics from all modules
   */
  public getMetrics(): IEmissionMetrics & {
    modules: {
      middleware: any;
      emission: any;
      deduplication: any;
      buffering: any;
      utilities: any;
    };
  } {
    return {
      ...this._metrics,
      modules: {
        middleware: this._middlewareManager?.getMetrics() || {},
        emission: this._emissionSystem?.getMetrics() || {},
        deduplication: this._deduplicationEngine?.getMetrics() || {},
        buffering: this._eventBuffering?.getMetrics() || {},
        utilities: this._utilities?.getMetrics() || {}
      }
    };
  }

  /**
   * Enable event buffering with configuration
   */
  public enableEventBuffering(config: IEventBuffering): void {
    this._config.buffering = config;
    // This would reinitialize the buffering module if needed
    this.logInfo('Event buffering configuration updated', { config });
  }

  /**
   * Emit event with buffering (explicit buffering call)
   */
  public async emitEventBuffered(
    eventType: string,
    data: unknown,
    options: IEmissionOptions = {}
  ): Promise<string> {
    if (!this._config.buffering?.enabled) {
      const result = await this.emitEvent(eventType, data, options);
      return result.eventId;
    }

    const bufferResult = await this._eventBuffering.bufferEvent(eventType, data, options);
    return bufferResult.bufferedEventId || this._generateOperationId();
  }

  /**
   * Manual buffer flush
   */
  public async flushEventBuffer(): Promise<any> {
    if (!this._eventBuffering) {
      throw new Error('Event buffering not initialized');
    }
    return await this._eventBuffering.flushBuffer();
  }

  /**
   * Get resilient metrics snapshot
   */
  public async getResilientMetricsSnapshot(): Promise<IResilientMetricsSnapshot | null> {
    if (!this._metricsCollector) return null;
    return await this._metricsCollector.getSnapshot();
  }

  // ============================================================================
  // PRIVATE UTILITY METHODS
  // ============================================================================

  /**
   * Generate operation ID with resilient timing
   */
  private _generateOperationId(): string {
    const timestamp = this._resilientTimer?.getCurrentTimestamp() || Date.now();
    return `op_${timestamp}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Record operation timing with resilient metrics
   */
  private async _recordOperationTiming(operation: string, timingResult: IResilientTimingResult): Promise<void> {
    if (this._config.enableMetrics && this._metricsCollector) {
      await this._metricsCollector.recordTiming(operation, timingResult);
    }
  }

  /**
   * Enhance error with timing context information
   */
  private _enhanceErrorWithTimingContext(error: unknown, context: IResilientTimingContext): Error {
    const enhancedError = error instanceof Error ? error : new Error(String(error));
    
    Object.assign(enhancedError, {
      resilientContext: context.getContext(),
      timingData: context.getSummary(),
      component: 'EventHandlerRegistryEnhanced',
      timestamp: new Date().toISOString()
    });
    
    return enhancedError;
  }
}
