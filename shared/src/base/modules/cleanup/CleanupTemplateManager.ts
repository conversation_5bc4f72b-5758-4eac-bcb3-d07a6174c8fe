/**
 * @file Cleanup Template Manager
 * @filepath shared/src/base/modules/cleanup/CleanupTemplateManager.ts
 * @task-id M-TSK-01.SUB-02.1.MOD-01
 * @component cleanup-template-manager
 * @reference foundation-context.CLEANUP-COORDINATION.002
 * @template modular-cleanup-template-management
 * @tier T1
 * @context cleanup-coordination-context
 * @category Cleanup-Modules
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Cleanup template management module providing:
 * - Comprehensive template creation, validation, and execution engine
 * - Template dependency graph management with resolution algorithms
 * - Template validation system with comprehensive rule checking
 * - Template workflow execution with performance tracking
 * - Memory-safe template operations with automatic cleanup
 * - Jest compatibility for testing environments
 * - Performance optimization with <5ms template processing overhead
 * - Integration with CleanupCoordinatorEnhanced for orchestrated operations
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON>Z. Consultancy"
 * @governance-adr ADR-foundation-003-cleanup-template-architecture
 * @governance-dcr DCR-foundation-003-cleanup-template-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @depends-on shared/src/base/modules/cleanup/TemplateDependencies
 * @enables shared/src/base/CleanupCoordinatorEnhanced
 * @enables shared/src/base/modules/cleanup/TemplateWorkflows
 * @related-contexts cleanup-coordination-context, foundation-context
 * @governance-impact framework-foundation, cleanup-management, template-processing
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type cleanup-modules
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/cleanup-coordination-context/modules/CleanupTemplateManager.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial template management implementation with dependency resolution
 * v1.1.0 (2025-07-28) - Added comprehensive validation and workflow execution systems
 */

import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { SimpleLogger, ILoggingService } from '../../LoggingMixin';
import {
  ICleanupTemplate,
  ITemplateExecution,
  ITemplateExecutionResult,
  ITemplateExecutionMetrics,
  IStepExecutionResult,
  IComponentRegistry,
  IEnhancedCleanupConfig
} from '../../types/CleanupTypes';
import {
  DEFAULT_ENHANCED_CLEANUP_CONFIG,
  createDefaultComponentRegistry
} from './CleanupConfiguration';
import { CleanupOperationType, CleanupPriority } from '../../CleanupCoordinator';
import { JestCompatibilityUtils, jestCompatibleYield } from '../../utils/JestCompatibilityUtils';

// RESILIENT TIMING INTEGRATION - Enterprise Infrastructure
import {
  ResilientTimer,
  IResilientTimingResult
} from '../../utils/ResilientTiming';

import {
  ResilientMetricsCollector
} from '../../utils/ResilientMetrics';

// Extracted module imports
import { createDependencyGraphFromOperations } from './TemplateDependencies';
import { 
  TemplateValidator, 
  validateTemplate,
  IExtendedValidationResult,
  ITemplateValidationConfig 
} from './TemplateValidation';
import { 
  TemplateWorkflowExecutor,
  createWorkflowExecutor,
  IWorkflowExecutionConfig,
  IStepExecutionOptions
} from './TemplateWorkflows';

/**
 * Enhanced Cleanup Template Manager
 * 
 * Manages template registration, validation, execution, and metrics collection
 * with comprehensive error handling, performance optimization, and Jest compatibility.
 */
export class CleanupTemplateManager extends MemorySafeResourceManager implements ILoggingService {
  private _logger: SimpleLogger;
  private _config: Required<IEnhancedCleanupConfig>;

  // Template storage and execution tracking
  private _templates = new Map<string, ICleanupTemplate>();
  private _templateExecutions = new Map<string, ITemplateExecution>();
  private _templateMetrics = new Map<string, ITemplateExecutionMetrics>();

  // Component registry for template operations
  private _componentRegistry: IComponentRegistry;
  
  // Extracted service instances
  private _templateValidator: TemplateValidator;
  private _workflowExecutor: TemplateWorkflowExecutor;

  // RESILIENT TIMING INFRASTRUCTURE - Dual-field pattern per prompt
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;

  constructor(config: Partial<IEnhancedCleanupConfig> = {}, componentRegistry?: IComponentRegistry) {
    super({
      maxIntervals: 25,
      maxTimeouts: 15,
      maxCacheSize: 8 * 1024 * 1024, // 8MB for template data
      memoryThresholdMB: 180,
      cleanupIntervalMs: 300000
    });

    this._logger = new SimpleLogger('CleanupTemplateManager');
    this._config = { ...DEFAULT_ENHANCED_CLEANUP_CONFIG, ...config };
    this._componentRegistry = componentRegistry || createDefaultComponentRegistry();

    // Initialize extracted services
    this._templateValidator = new TemplateValidator({
      strictMode: this._config.templateValidationEnabled,
      validateDependencies: true,
      validateConditions: true,
      validateParameters: true
    });

    this._workflowExecutor = createWorkflowExecutor(this._componentRegistry, {
      testMode: this._config.testMode,
      enableParallelExecution: true,
      continueOnStepFailure: false
    });
  }

  // Implement ILoggingService interface
  logInfo(message: string, metadata?: Record<string, any>): void {
    this._logger.logInfo(message, metadata);
  }

  logWarning(message: string, metadata?: Record<string, any>): void {
    this._logger.logWarning(message, metadata);
  }

  logError(message: string, error?: Error, metadata?: Record<string, any>): void {
    this._logger.logError(message, error, metadata);
  }

  logDebug(message: string, metadata?: Record<string, any>): void {
    this._logger.logDebug(message, metadata);
  }

  protected async doInitialize(): Promise<void> {
    this.logInfo('CleanupTemplateManager initializing', {
      templateValidationEnabled: this._config.templateValidationEnabled,
      testMode: this._config.testMode
    });

    // RESILIENT TIMING INFRASTRUCTURE - Enterprise Configuration per prompt
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: this._config.defaultTimeout || 30000, // Use config or default
      unreliableThreshold: 3, // 3 consecutive failures = unreliable
      estimateBaseline: 100 // 100ms baseline for template operations
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      cacheUnreliableValues: false,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['template_registration', 200],
        ['template_validation', 500],
        ['template_execution', 2000],
        ['step_execution', 300],
        ['dependency_resolution', 150],
        ['workflow_execution', 1500]
      ])
    });

    this.logInfo('CleanupTemplateManager resilient timing infrastructure initialized', {
      timerFallbacksEnabled: true,
      metricsCollectionEnabled: true,
      performanceTarget: 'enterprise',
      configuredTimeout: this._config.defaultTimeout,
      metricsEnabled: this._config.metricsEnabled
    });

    // LESSON LEARNED: Avoid constructor-time resource allocation
    // Initialize collections without creating external resources
    this._templates.clear();
    this._templateExecutions.clear();
    this._templateMetrics.clear();
  }

  protected async doShutdown(): Promise<void> {
    this.logInfo('CleanupTemplateManager shutting down', {
      templatesRegistered: this._templates.size,
      activeExecutions: this._templateExecutions.size
    });

    try {
      // Cancel any running template executions
      // ES6+ COMPLIANT: iteration using Array.from and forEach
      Array.from(this._templateExecutions.values()).forEach(execution => {
        if (execution.status === 'running') {
          execution.status = 'cancelled';
          execution.endTime = new Date();
        }
      });

      this._templates.clear();
      this._templateExecutions.clear();
      this._templateMetrics.clear();

    } catch (error) {
      this.logError('Error during template manager shutdown',
        error instanceof Error ? error : new Error(String(error)));
    }

    // RESILIENT TIMING INFRASTRUCTURE CLEANUP - Per prompt requirements
    try {
      if (this._metricsCollector) {
        // Get final metrics snapshot before shutdown
        const finalSnapshot = this._metricsCollector.createSnapshot();

        this.logInfo('CleanupTemplateManager final resilient metrics snapshot', {
          totalMetrics: finalSnapshot.metrics.size,
          reliable: finalSnapshot.reliable,
          warnings: finalSnapshot.warnings.length
        });

        // Reset metrics collector
        this._metricsCollector.reset();
      }

      this.logInfo('CleanupTemplateManager resilient timing infrastructure shutdown completed successfully');

    } catch (timingError) {
      this.logError('Error during CleanupTemplateManager resilient timing infrastructure shutdown',
        timingError instanceof Error ? timingError : new Error(String(timingError)));
    }
  }

  // ============================================================================
  // TEMPLATE REGISTRATION & MANAGEMENT
  // ============================================================================

  /**
   * Register cleanup template for reusable workflows
   * LESSON LEARNED: Comprehensive validation with Jest-compatible operations
   */
  public async registerTemplate(template: ICleanupTemplate): Promise<void> {
    // CONTEXT-BASED TIMING - Create timing context per prompt requirements
    const registrationContext = this._resilientTimer.start();

    try {
      // LESSON LEARNED: Async yielding for Jest compatibility
      await jestCompatibleYield();

      this.logInfo('Registering cleanup template', {
        templateId: template.id,
        operationCount: template.operations.length,
        hasRollback: template.rollbackSteps?.length > 0
      });
      // Validate template if validation is enabled
      if (this._config.templateValidationEnabled) {
        const validation = await this._templateValidator.validateTemplate(template);
        if (!validation.valid) {
          const errorMessage = `Template validation failed: ${validation.issues.map(i => i.message).join(', ')}`;
          throw new Error(errorMessage);
        }

        if (validation.warnings.length > 0) {
          this.logWarning('Template validation warnings', {
            templateId: template.id,
            warnings: validation.warnings
          });
        }
      }

      // Check for dependency cycles using extracted module
      const dependencyGraph = createDependencyGraphFromOperations(template.operations);
      if (dependencyGraph.hasCycles()) {
        throw new Error(`Template ${template.id} contains circular dependencies`);
      }

      // Store template
      this._templates.set(template.id, template);

      // Initialize metrics
      this._templateMetrics.set(template.id, {
        totalSteps: template.operations.length,
        executedSteps: 0,
        failedSteps: 0,
        skippedSteps: 0,
        averageStepTime: 0,
        longestStepTime: 0,
        dependencyResolutionTime: 0,
        validationTime: 0,
        totalExecutionTime: 0
      });

      this.logInfo('Template registered successfully', {
        templateId: template.id,
        operationCount: template.operations.length,
        dependencyGraphSize: dependencyGraph.nodes.size
      });

      // Record successful registration timing
      const registrationResult = registrationContext.end();
      this._metricsCollector.recordTiming('template_registration', registrationResult);

    } catch (error) {
      // Record failed registration timing
      const registrationResult = registrationContext.end();
      this._metricsCollector.recordTiming('template_registration_failed', registrationResult);

      const templateError = this._enhanceErrorContext(error instanceof Error ? error : new Error(String(error)), {
        context: 'template_registration',
        templateId: template.id,
        component: 'CleanupTemplateManager'
      });

      this.logError('Template registration failed', templateError, {
        templateId: template.id,
        registrationTime: registrationResult.duration
      });
      throw templateError;
    }
  }

  /**
   * Execute cleanup template with specified target components
   * LESSON LEARNED: Jest-compatible execution with proper async yielding
   */
  public async executeTemplate(
    templateId: string,
    targetComponents: string[],
    parameters: Record<string, any> = {}
  ): Promise<ITemplateExecutionResult> {
    // CONTEXT-BASED TIMING - Create timing context per prompt requirements
    const executionContext = this._resilientTimer.start();

    const template = this._templates.get(templateId);
    if (!template) {
      throw new Error(`Template ${templateId} not found`);
    }

    const executionId = this._generateExecutionId(templateId);
    const startTime = performance.now();

    this.logInfo('Starting template execution', {
      templateId,
      executionId,
      targetComponentCount: targetComponents.length,
      parameterCount: Object.keys(parameters).length
    });

    try {
      // Create execution context
      const execution: ITemplateExecution = {
        id: executionId,
        templateId,
        targetComponents: [...targetComponents],
        parameters: { ...parameters },
        status: 'running',
        startTime: new Date(),
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: template.operations.length,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };

      this._templateExecutions.set(executionId, execution);

      // Execute template steps using extracted workflow executor
      const stepResults = await this._workflowExecutor.executeWorkflow(template, execution);

      // Store step results in execution for metrics calculation
      stepResults.forEach(result => {
        execution.stepResults.set(`${result.stepId}-${result.componentId}`, result);
      });

      // Update execution status
      execution.status = stepResults.every(r => r.success) ? 'completed' : 'failed';
      execution.endTime = new Date();
      execution.metrics.totalExecutionTime = performance.now() - startTime;

      // Update template metrics
      this._updateTemplateMetrics(templateId, execution);

      const result: ITemplateExecutionResult = {
        executionId,
        templateId,
        status: execution.status === 'completed' ? 'success' : 'failure',
        executedSteps: stepResults.filter(r => !r.skipped).length,
        totalSteps: template.operations.length,
        failedSteps: stepResults.filter(r => !r.success && !r.skipped).length,
        skippedSteps: stepResults.filter(r => r.skipped).length,
        executionTime: execution.metrics.totalExecutionTime,
        results: stepResults,
        rollbackExecuted: execution.rollbackExecuted,
        warnings: [],
        errors: stepResults.filter(r => r.error).map(r => r.error!).filter((e): e is Error => e instanceof Error)
      };

      this.logInfo('Template execution completed', {
        templateId,
        executionId,
        status: execution.status,
        executionTime: execution.metrics.totalExecutionTime,
        successfulSteps: stepResults.filter(r => r.success).length,
        totalSteps: stepResults.length
      });

      // Record successful execution timing
      const executionResult = executionContext.end();
      this._metricsCollector.recordTiming('template_execution', executionResult);

      return result;

    } catch (error) {
      // Record failed execution timing
      const executionResult = executionContext.end();
      this._metricsCollector.recordTiming('template_execution_failed', executionResult);

      const executionError = this._enhanceErrorContext(error instanceof Error ? error : new Error(String(error)), {
        context: 'template_execution',
        templateId,
        executionId,
        component: 'CleanupTemplateManager'
      });

      this.logError('Template execution failed', executionError, {
        templateId,
        executionId,
        executionTime: performance.now() - startTime,
        timingDuration: executionResult.duration
      });

      return {
        executionId,
        templateId,
        status: 'failure',
        executedSteps: 0,
        totalSteps: template.operations.length,
        failedSteps: template.operations.length,
        skippedSteps: 0,
        executionTime: performance.now() - startTime,
        results: [],
        rollbackExecuted: false,
        warnings: [],
        errors: [executionError]
      };
    }
  }

  /**
   * Get available templates
   */
  public getTemplates(): ICleanupTemplate[] {
    return Array.from(this._templates.values());
  }

  /**
   * Get template execution metrics
   */
  public getTemplateMetrics(templateId?: string): ITemplateExecutionMetrics | Record<string, ITemplateExecutionMetrics> {
    if (templateId) {
      return this._templateMetrics.get(templateId) || {
        totalSteps: 0,
        executedSteps: 0,
        failedSteps: 0,
        skippedSteps: 0,
        averageStepTime: 0,
        longestStepTime: 0,
        dependencyResolutionTime: 0,
        validationTime: 0,
        totalExecutionTime: 0
      };
    }

    const allMetrics: Record<string, ITemplateExecutionMetrics> = {};
    // ES6+ COMPLIANT: Use forEach instead of for...of
    this._templateMetrics.forEach((metrics, id) => {
      allMetrics[id] = metrics;
    });
    return allMetrics;
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Update template metrics based on execution
   * LESSON LEARNED: Proper metrics accumulation for test validation
   */
  private _updateTemplateMetrics(templateId: string, execution: ITemplateExecution): void {
    const metrics = this._templateMetrics.get(templateId);
    if (!metrics) return;

    const stepResults = Array.from(execution.stepResults.values());

    // Update step counts
    metrics.executedSteps += stepResults.filter(r => !r.skipped).length;
    metrics.failedSteps += stepResults.filter(r => !r.success && !r.skipped).length;
    metrics.skippedSteps += stepResults.filter(r => r.skipped).length;

    // Update timing metrics - CRITICAL FIX: Accumulate total execution time
    metrics.totalExecutionTime += execution.metrics.totalExecutionTime;

    if (stepResults.length > 0) {
      const stepTimes = stepResults.map(r => r.executionTime);
      const avgStepTime = stepTimes.reduce((sum, time) => sum + time, 0) / stepTimes.length;
      const maxStepTime = Math.max(...stepTimes);

      // LESSON LEARNED: Proper average calculation for cumulative metrics
      if (metrics.averageStepTime === 0) {
        metrics.averageStepTime = avgStepTime;
      } else {
        metrics.averageStepTime = (metrics.averageStepTime + avgStepTime) / 2;
      }
      metrics.longestStepTime = Math.max(metrics.longestStepTime, maxStepTime);
    }

    this._templateMetrics.set(templateId, metrics);
  }

  /**
   * Generate unique execution ID
   */
  private _generateExecutionId(templateId: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `template-exec-${templateId}-${timestamp}-${random}`;
  }

  /**
   * Enhance error context with timing information
   */
  private _enhanceErrorContext(error: Error, context: {
    context: string;
    templateId?: string;
    executionId?: string;
    component: string
  }): Error {
    const enhancedError = new Error(
      `${error.message} (Context: ${context.context}, Template: ${context.templateId || 'unknown'}, Component: ${context.component})`
    );
    enhancedError.name = error.name;
    enhancedError.stack = error.stack;
    return enhancedError;
  }
}